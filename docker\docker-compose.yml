version: '3.8'

services:
    app:
        build:
            context: ..
            dockerfile: docker/php/Dockerfile
        container_name: filament-app
        ports:
            - "8080:80"
        volumes:
            - ../:/var/www/html:delegated
            - ./apache/apache.conf:/etc/apache2/sites-available/000-default.conf
            - ./php.ini:/usr/local/etc/php/php.ini
        environment:
            - APACHE_LOG_DIR=/var/log/apache2

    db:
        image: mysql:8.0
        container_name: filament-db
        restart: always
        environment:
            MYSQL_ROOT_PASSWORD: root
            MYSQL_DATABASE: filament
            MYSQL_USER: filament_user
            MYSQL_PASSWORD: password
        ports:
            - "3306:3306"
        volumes:
            - db_data:/var/lib/mysql
            - ./my.cnf:/etc/mysql/conf.d/my.cnf

    phpmyadmin:
        image: phpmyadmin:latest
        container_name: phpmyadmin
        restart: always
        environment:
            PMA_HOST: db
            MYSQL_ROOT_PASSWORD: root
        ports:
            - "8081:80"

volumes:
    db_data:
