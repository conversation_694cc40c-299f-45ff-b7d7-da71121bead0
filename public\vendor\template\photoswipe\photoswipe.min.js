/*! PhotoSwipe - v4.1.3 - 2019-01-08
* http://photoswipe.com
* Copyright (c) 2019 <PERSON>; */
!function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.PhotoSwipe=t()}(this,function(){"use strict";return function(m,n,e,t){var f={features:null,bind:function(e,t,n,i){var o=(i?"remove":"add")+"EventListener";t=t.split(" ");for(var a=0;a<t.length;a++)t[a]&&e[o](t[a],n,!1)},isArray:function(e){return e instanceof Array},createEl:function(e,t){t=document.createElement(t||"div");return e&&(t.className=e),t},getScrollY:function(){var e=window.pageYOffset;return e!==undefined?e:document.documentElement.scrollTop},unbind:function(e,t,n){f.bind(e,t,n,!0)},removeClass:function(e,t){t=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className=e.className.replace(t," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")},addClass:function(e,t){f.hasClass(e,t)||(e.className+=(e.className?" ":"")+t)},hasClass:function(e,t){return e.className&&new RegExp("(^|\\s)"+t+"(\\s|$)").test(e.className)},getChildByClass:function(e,t){for(var n=e.firstChild;n;){if(f.hasClass(n,t))return n;n=n.nextSibling}},arraySearch:function(e,t,n){for(var i=e.length;i--;)if(e[i][n]===t)return i;return-1},extend:function(e,t,n){for(var i in t)t.hasOwnProperty(i)&&(n&&e.hasOwnProperty(i)||(e[i]=t[i]))},easing:{sine:{out:function(e){return Math.sin(e*(Math.PI/2))},inOut:function(e){return-(Math.cos(Math.PI*e)-1)/2}},cubic:{out:function(e){return--e*e*e+1}}},detectFeatures:function(){if(f.features)return f.features;var e,t,n=f.createEl().style,i="",o={};o.oldIE=document.all&&!document.addEventListener,o.touch="ontouchstart"in window,window.requestAnimationFrame&&(o.raf=window.requestAnimationFrame,o.caf=window.cancelAnimationFrame),o.pointerEvent=!!window.PointerEvent||navigator.msPointerEnabled,o.pointerEvent||(e=navigator.userAgent,!/iP(hone|od)/.test(navigator.platform)||(t=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/))&&0<t.length&&1<=(t=parseInt(t[1],10))&&t<8&&(o.isOldIOSPhone=!0),t=(t=e.match(/Android\s([0-9\.]*)/))?t[1]:0,1<=(t=parseFloat(t))&&(t<4.4&&(o.isOldAndroid=!0),o.androidVersion=t),o.isMobileOpera=/opera mini|opera mobi/i.test(e));for(var a,r,l,s=["transform","perspective","animationName"],u=["","webkit","Moz","ms","O"],c=0;c<4;c++){i=u[c];for(var d=0;d<3;d++)a=s[d],r=i+(i?a.charAt(0).toUpperCase()+a.slice(1):a),!o[a]&&r in n&&(o[a]=r);i&&!o.raf&&(i=i.toLowerCase(),o.raf=window[i+"RequestAnimationFrame"],o.raf&&(o.caf=window[i+"CancelAnimationFrame"]||window[i+"CancelRequestAnimationFrame"]))}return o.raf||(l=0,o.raf=function(e){var t=(new Date).getTime(),n=Math.max(0,16-(t-l)),i=window.setTimeout(function(){e(t+n)},n);return l=t+n,i},o.caf=function(e){clearTimeout(e)}),o.svg=!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect,f.features=o}};f.detectFeatures(),f.features.oldIE&&(f.bind=function(e,t,n,i){t=t.split(" ");for(var o,a=(i?"detach":"attach")+"Event",r=function(){n.handleEvent.call(n)},l=0;l<t.length;l++)if(o=t[l])if("object"==typeof n&&n.handleEvent){if(i){if(!n["oldIE"+o])return!1}else n["oldIE"+o]=r;e[a]("on"+o,n["oldIE"+o])}else e[a]("on"+o,n)});var h=this,y={allowPanToNext:!0,spacing:.12,bgOpacity:1,mouseUsed:!1,loop:!0,pinchToClose:!0,closeOnScroll:!0,closeOnVerticalDrag:!0,verticalDragRange:.75,hideAnimationDuration:333,showAnimationDuration:333,showHideOpacity:!1,focus:!0,escKey:!0,arrowKeys:!0,mainScrollEndFriction:.35,panEndFriction:.35,isClickableElement:function(e){return"A"===e.tagName},getDoubleTapZoom:function(e,t){return e||t.initialZoomLevel<.7?1:1.33},maxSpreadZoom:1.33,modal:!0,scaleMode:"fit"};f.extend(y,t);var r,i,o,x,a,l,s,u,c,g,d,p,v,w,b,I,C,D,T,M,S,A,E,O,k,R,P,Z,F,L,z,_,N,U,H,Y,W,B,G,X,V,K,q,$,j,J,Q,ee,te,ne,ie,oe,ae,re,le,se,ue=function(){return{x:0,y:0}},ce=ue(),de=ue(),pe=ue(),me={},fe=0,he={},ye=ue(),xe=0,ge=!0,ve=[],we={},be=!1,Ie=function(e,t){f.extend(h,t.publicMethods),ve.push(e)},Ce=function(e){var t=Ht();return t-1<e?e-t:e<0?t+e:e},De={},Te=function(e,t){return De[e]||(De[e]=[]),De[e].push(t)},Me=function(e){var t=De[e];if(t){var n=Array.prototype.slice.call(arguments);n.shift();for(var i=0;i<t.length;i++)t[i].apply(h,n)}},Se=function(){return(new Date).getTime()},Ae=function(e){re=e,h.bg.style.opacity=e*y.bgOpacity},Ee=function(e,t,n,i,o){(!be||o&&o!==h.currItem)&&(i/=(o||h.currItem).fitRatio),e[A]=p+t+"px, "+n+"px"+v+" scale("+i+")"},Oe=function(e){te&&(e&&(g>h.currItem.fitRatio?be||(jt(h.currItem,!1,!0),be=!0):be&&(jt(h.currItem),be=!1)),Ee(te,pe.x,pe.y,g))},ke=function(e){e.container&&Ee(e.container.style,e.initialPosition.x,e.initialPosition.y,e.initialZoomLevel,e)},Re=function(e,t){t[A]=p+e+"px, 0px"+v},Pe=function(e,t){var n;!y.loop&&t&&(n=x+(ye.x*fe-e)/ye.x,t=Math.round(e-ct.x),(n<0&&0<t||n>=Ht()-1&&t<0)&&(e=ct.x+t*y.mainScrollEndFriction)),ct.x=e,Re(e,a)},Ze=function(e,t){var n=dt[e]-he[e];return de[e]+ce[e]+n-t/d*n},Fe=function(e,t){e.x=t.x,e.y=t.y,t.id&&(e.id=t.id)},Le=function(e){e.x=Math.round(e.x),e.y=Math.round(e.y)},ze=null,_e=function(){ze&&(f.unbind(document,"mousemove",_e),f.addClass(m,"pswp--has_mouse"),y.mouseUsed=!0,Me("mouseUsed")),ze=setTimeout(function(){ze=null},100)},Ne=function(e,t){e=Vt(h.currItem,me,e);return t&&(ee=e),e},Ue=function(e){return(e=e||h.currItem).initialZoomLevel},He=function(e){return 0<(e=e||h.currItem).w?y.maxSpreadZoom:1},Ye=function(e,t,n,i){return i===h.currItem.initialZoomLevel?(n[e]=h.currItem.initialPosition[e],!0):(n[e]=Ze(e,i),n[e]>t.min[e]?(n[e]=t.min[e],!0):n[e]<t.max[e]&&(n[e]=t.max[e],!0))},We=function(e){var t="";y.escKey&&27===e.keyCode?t="close":y.arrowKeys&&(37===e.keyCode?t="prev":39===e.keyCode&&(t="next")),t&&(e.ctrlKey||e.altKey||e.shiftKey||e.metaKey||(e.preventDefault?e.preventDefault():e.returnValue=!1,h[t]()))},Be=function(e){e&&(K||V||ne||W)&&(e.preventDefault(),e.stopPropagation())},Ge=function(){h.setScrollOffset(0,f.getScrollY())},Xe={},Ve=0,Ke=function(e){Xe[e]&&(Xe[e].raf&&R(Xe[e].raf),Ve--,delete Xe[e])},qe=function(e){Xe[e]&&Ke(e),Xe[e]||(Ve++,Xe[e]={})},$e=function(){for(var e in Xe)Xe.hasOwnProperty(e)&&Ke(e)},je=function(e,t,n,i,o,a,r){var l,s=Se();qe(e);var u=function(){if(Xe[e]){if(l=Se()-s,i<=l)return Ke(e),a(n),void(r&&r());a((n-t)*o(l/i)+t),Xe[e].raf=k(u)}};u()},t={shout:Me,listen:Te,viewportSize:me,options:y,isMainScrollAnimating:function(){return ne},getZoomLevel:function(){return g},getCurrentIndex:function(){return x},isDragging:function(){return G},isZooming:function(){return J},setScrollOffset:function(e,t){he.x=e,L=he.y=t,Me("updateScrollOffset",he)},applyZoomPan:function(e,t,n,i){pe.x=t,pe.y=n,g=e,Oe(i)},init:function(){if(!r&&!i){var e;h.framework=f,h.template=m,h.bg=f.getChildByClass(m,"pswp__bg"),P=m.className,r=!0,z=f.detectFeatures(),k=z.raf,R=z.caf,A=z.transform,F=z.oldIE,h.scrollWrap=f.getChildByClass(m,"pswp__scroll-wrap"),h.container=f.getChildByClass(h.scrollWrap,"pswp__container"),a=h.container.style,h.itemHolders=I=[{el:h.container.children[0],wrap:0,index:-1},{el:h.container.children[1],wrap:0,index:-1},{el:h.container.children[2],wrap:0,index:-1}],I[0].el.style.display=I[2].el.style.display="none",function(){if(A){var e=z.perspective&&!O;return p="translate"+(e?"3d(":"("),v=z.perspective?", 0px)":")"}A="left",f.addClass(m,"pswp--ie"),Re=function(e,t){t.left=e+"px"},ke=function(e){var t=1<e.fitRatio?1:e.fitRatio,n=e.container.style,i=t*e.w,t=t*e.h;n.width=i+"px",n.height=t+"px",n.left=e.initialPosition.x+"px",n.top=e.initialPosition.y+"px"},Oe=function(){var e,t,n,i;te&&(e=te,n=(t=1<(i=h.currItem).fitRatio?1:i.fitRatio)*i.w,i=t*i.h,e.width=n+"px",e.height=i+"px",e.left=pe.x+"px",e.top=pe.y+"px")}}(),c={resize:h.updateSize,orientationchange:function(){clearTimeout(_),_=setTimeout(function(){me.x!==h.scrollWrap.clientWidth&&h.updateSize()},500)},scroll:Ge,keydown:We,click:Be};var t=z.isOldIOSPhone||z.isOldAndroid||z.isMobileOpera;for(z.animationName&&z.transform&&!t||(y.showAnimationDuration=y.hideAnimationDuration=0),e=0;e<ve.length;e++)h["init"+ve[e]]();n&&(h.ui=new n(h,f)).init(),Me("firstUpdate"),x=x||y.index||0,(isNaN(x)||x<0||x>=Ht())&&(x=0),h.currItem=Ut(x),(z.isOldIOSPhone||z.isOldAndroid)&&(ge=!1),m.setAttribute("aria-hidden","false"),y.modal&&(ge?m.style.position="fixed":(m.style.position="absolute",m.style.top=f.getScrollY()+"px")),L===undefined&&(Me("initialLayout"),L=Z=f.getScrollY());t="pswp--open ";for(y.mainClass&&(t+=y.mainClass+" "),y.showHideOpacity&&(t+="pswp--animate_opacity "),t+=O?"pswp--touch":"pswp--notouch",t+=z.animationName?" pswp--css_animation":"",t+=z.svg?" pswp--svg":"",f.addClass(m,t),h.updateSize(),l=-1,xe=null,e=0;e<3;e++)Re((e+l)*ye.x,I[e].el.style);F||f.bind(h.scrollWrap,u,h),Te("initialZoomInEnd",function(){h.setContent(I[0],x-1),h.setContent(I[2],x+1),I[0].el.style.display=I[2].el.style.display="block",y.focus&&m.focus(),f.bind(document,"keydown",h),z.transform&&f.bind(h.scrollWrap,"click",h),y.mouseUsed||f.bind(document,"mousemove",_e),f.bind(window,"resize scroll orientationchange",h),Me("bindEvents")}),h.setContent(I[1],x),h.updateCurrItem(),Me("afterInit"),ge||(w=setInterval(function(){Ve||G||J||g!==h.currItem.initialZoomLevel||h.updateSize()},1e3)),f.addClass(m,"pswp--visible")}},close:function(){r&&(i=!(r=!1),Me("close"),f.unbind(window,"resize scroll orientationchange",h),f.unbind(window,"scroll",c.scroll),f.unbind(document,"keydown",h),f.unbind(document,"mousemove",_e),z.transform&&f.unbind(h.scrollWrap,"click",h),G&&f.unbind(window,s,h),clearTimeout(_),Me("unbindEvents"),Yt(h.currItem,null,!0,h.destroy))},destroy:function(){Me("destroy"),Lt&&clearTimeout(Lt),m.setAttribute("aria-hidden","true"),m.className=P,w&&clearInterval(w),f.unbind(h.scrollWrap,u,h),f.unbind(window,"scroll",h),ft(),$e(),De=null},panTo:function(e,t,n){n||(e>ee.min.x?e=ee.min.x:e<ee.max.x&&(e=ee.max.x),t>ee.min.y?t=ee.min.y:t<ee.max.y&&(t=ee.max.y)),pe.x=e,pe.y=t,Oe()},handleEvent:function(e){e=e||window.event,c[e.type]&&c[e.type](e)},goTo:function(e){var t=(e=Ce(e))-x;xe=t,x=e,h.currItem=Ut(x),fe-=t,Pe(ye.x*fe),$e(),ne=!1,h.updateCurrItem()},next:function(){h.goTo(x+1)},prev:function(){h.goTo(x-1)},updateCurrZoomItem:function(e){var t;e&&Me("beforeChange",0),te=I[1].el.children.length?(t=I[1].el.children[0],f.hasClass(t,"pswp__zoom-wrap")?t.style:null):null,ee=h.currItem.bounds,d=g=h.currItem.initialZoomLevel,pe.x=ee.center.x,pe.y=ee.center.y,e&&Me("afterChange")},invalidateCurrItems:function(){b=!0;for(var e=0;e<3;e++)I[e].item&&(I[e].item.needsUpdate=!0)},updateCurrItem:function(e){if(0!==xe){var t,n=Math.abs(xe);if(!(e&&n<2)){h.currItem=Ut(x),be=!1,Me("beforeChange",xe),3<=n&&(l+=xe+(0<xe?-3:3),n=3);for(var i=0;i<n;i++)0<xe?(t=I.shift(),I[2]=t,Re((++l+2)*ye.x,t.el.style),h.setContent(t,x-n+i+1+1)):(t=I.pop(),I.unshift(t),Re(--l*ye.x,t.el.style),h.setContent(t,x+n-i-1-1));!te||1!==Math.abs(xe)||(e=Ut(C)).initialZoomLevel!==g&&(Vt(e,me),jt(e),ke(e)),xe=0,h.updateCurrZoomItem(),C=x,Me("afterChange")}}},updateSize:function(e){if(!ge&&y.modal){var t=f.getScrollY();if(L!==t&&(m.style.top=t+"px",L=t),!e&&we.x===window.innerWidth&&we.y===window.innerHeight)return;we.x=window.innerWidth,we.y=window.innerHeight,m.style.height=we.y+"px"}if(me.x=h.scrollWrap.clientWidth,me.y=h.scrollWrap.clientHeight,Ge(),ye.x=me.x+Math.round(me.x*y.spacing),ye.y=me.y,Pe(ye.x*fe),Me("beforeResize"),l!==undefined){for(var n,i,o,a=0;a<3;a++)n=I[a],Re((a+l)*ye.x,n.el.style),o=x+a-1,y.loop&&2<Ht()&&(o=Ce(o)),(i=Ut(o))&&(b||i.needsUpdate||!i.bounds)?(h.cleanSlide(i),h.setContent(n,o),1===a&&(h.currItem=i,h.updateCurrZoomItem(!0)),i.needsUpdate=!1):-1===n.index&&0<=o&&h.setContent(n,o),i&&i.container&&(Vt(i,me),jt(i),ke(i));b=!1}d=g=h.currItem.initialZoomLevel,(ee=h.currItem.bounds)&&(pe.x=ee.center.x,pe.y=ee.center.y,Oe(!0)),Me("resize")},zoomTo:function(t,e,n,i,o){e&&(d=g,dt.x=Math.abs(e.x)-pe.x,dt.y=Math.abs(e.y)-pe.y,Fe(de,pe));var e=Ne(t,!1),a={};Ye("x",e,a,t),Ye("y",e,a,t);var r=g,l=pe.x,s=pe.y;Le(a);e=function(e){1===e?(g=t,pe.x=a.x,pe.y=a.y):(g=(t-r)*e+r,pe.x=(a.x-l)*e+l,pe.y=(a.y-s)*e+s),o&&o(e),Oe(1===e)};n?je("customZoomTo",0,1,n,i||f.easing.sine.inOut,e):e(1)}},Je={},Qe={},et={},tt={},nt={},it=[],ot={},at=[],rt={},lt=0,st=ue(),ut=0,ct=ue(),dt=ue(),pt=ue(),mt=function(e,t){return rt.x=Math.abs(e.x-t.x),rt.y=Math.abs(e.y-t.y),Math.sqrt(rt.x*rt.x+rt.y*rt.y)},ft=function(){q&&(R(q),q=null)},ht=function(){G&&(q=k(ht),Et())},yt=function(e,t){return!(!e||e===document)&&(!(e.getAttribute("class")&&-1<e.getAttribute("class").indexOf("pswp__scroll-wrap"))&&(t(e)?e:yt(e.parentNode,t)))},xt={},gt=function(e,t){return xt.prevent=!yt(e.target,y.isClickableElement),Me("preventDragEvent",e,t,xt),xt.prevent},vt=function(e,t){return t.x=e.pageX,t.y=e.pageY,t.id=e.identifier,t},wt=function(e,t,n){n.x=.5*(e.x+t.x),n.y=.5*(e.y+t.y)},bt=function(){var e=pe.y-h.currItem.initialPosition.y;return 1-Math.abs(e/(me.y/2))},It={},Ct={},Dt=[],Tt=function(e){for(;0<Dt.length;)Dt.pop();return E?(se=0,it.forEach(function(e){0===se?Dt[0]=e:1===se&&(Dt[1]=e),se++})):-1<e.type.indexOf("touch")?e.touches&&0<e.touches.length&&(Dt[0]=vt(e.touches[0],It),1<e.touches.length&&(Dt[1]=vt(e.touches[1],Ct))):(It.x=e.pageX,It.y=e.pageY,It.id="",Dt[0]=It),Dt},Mt=function(e,t){var n,i,o,a=pe[e]+t[e],r=0<t[e],l=ct.x+t.x,s=ct.x-ot.x,u=a>ee.min[e]||a<ee.max[e]?y.panEndFriction:1,a=pe[e]+t[e]*u;if((y.allowPanToNext||g===h.currItem.initialZoomLevel)&&(te?"h"!==ie||"x"!==e||V||(r?(a>ee.min[e]&&(u=y.panEndFriction,ee.min[e],n=ee.min[e]-de[e]),(n<=0||s<0)&&1<Ht()?(o=l,s<0&&l>ot.x&&(o=ot.x)):ee.min.x!==ee.max.x&&(i=a)):(a<ee.max[e]&&(u=y.panEndFriction,ee.max[e],n=de[e]-ee.max[e]),(n<=0||0<s)&&1<Ht()?(o=l,0<s&&l<ot.x&&(o=ot.x)):ee.min.x!==ee.max.x&&(i=a))):o=l,"x"===e))return o!==undefined&&(Pe(o,!0),$=o!==ot.x),ee.min.x!==ee.max.x&&(i!==undefined?pe.x=i:$||(pe.x+=t.x*u)),o!==undefined;ne||$||g>h.currItem.fitRatio&&(pe[e]+=t[e]*u)},St=function(e){var t;"mousedown"===e.type&&0<e.button||(Nt?e.preventDefault():B&&"mousedown"===e.type||(gt(e,!0)&&e.preventDefault(),Me("pointerDown"),E&&((t=f.arraySearch(it,e.pointerId,"id"))<0&&(t=it.length),it[t]={x:e.pageX,y:e.pageY,id:e.pointerId}),e=(t=Tt(e)).length,j=null,$e(),G&&1!==e||(G=oe=!0,f.bind(window,s,h),Y=le=ae=W=$=K=X=V=!1,ie=null,Me("firstTouchStart",t),Fe(de,pe),ce.x=ce.y=0,Fe(tt,t[0]),Fe(nt,tt),ot.x=ye.x*fe,at=[{x:tt.x,y:tt.y}],U=N=Se(),Ne(g,!0),ft(),ht()),!J&&1<e&&!ne&&!$&&(d=g,J=X=!(V=!1),ce.y=ce.x=0,Fe(de,pe),Fe(Je,t[0]),Fe(Qe,t[1]),wt(Je,Qe,pt),dt.x=Math.abs(pt.x)-pe.x,dt.y=Math.abs(pt.y)-pe.y,Q=mt(Je,Qe))))},At=function(e){var t,n;e.preventDefault(),!E||-1<(t=f.arraySearch(it,e.pointerId,"id"))&&((n=it[t]).x=e.pageX,n.y=e.pageY),G&&(n=Tt(e),ie||K||J?j=n:ct.x!==ye.x*fe?ie="h":(e=Math.abs(n[0].x-tt.x)-Math.abs(n[0].y-tt.y),10<=Math.abs(e)&&(ie=0<e?"h":"v",j=n)))},Et=function(){if(j){var e,t,n,i,o,a=j.length;if(0!==a)if(Fe(Je,j[0]),et.x=Je.x-tt.x,et.y=Je.y-tt.y,J&&1<a)tt.x=Je.x,tt.y=Je.y,(et.x||et.y||(i=j[1],o=Qe,i.x!==o.x||i.y!==o.y))&&(Fe(Qe,j[1]),V||(V=!0,Me("zoomGestureStarted")),t=mt(Je,Qe),(n=Zt(t))>h.currItem.initialZoomLevel+h.currItem.initialZoomLevel/15&&(le=!0),e=1,a=Ue(),i=He(),n<a?y.pinchToClose&&!le&&d<=h.currItem.initialZoomLevel?(Ae(o=1-(a-n)/(a/1.2)),Me("onPinchClose",o),ae=!0):(1<(e=(a-n)/a)&&(e=1),n=a-e*(a/3)):i<n&&(1<(e=(n-i)/(6*a))&&(e=1),n=i+e*a),e<0&&(e=0),wt(Je,Qe,st),ce.x+=st.x-pt.x,ce.y+=st.y-pt.y,Fe(pt,st),pe.x=Ze("x",n),pe.y=Ze("y",n),Y=g<n,g=n,Oe());else if(ie&&(oe&&(oe=!1,10<=Math.abs(et.x)&&(et.x-=j[0].x-nt.x),10<=Math.abs(et.y)&&(et.y-=j[0].y-nt.y)),tt.x=Je.x,tt.y=Je.y,0!==et.x||0!==et.y)){if("v"===ie&&y.closeOnVerticalDrag&&"fit"===y.scaleMode&&g===h.currItem.initialZoomLevel){ce.y+=et.y,pe.y+=et.y;var r=bt();return W=!0,Me("onVerticalDrag",r),Ae(r),void Oe()}e=Se(),t=Je.x,n=Je.y,50<e-U&&((r=2<at.length?at.shift():{}).x=t,r.y=n,at.push(r),U=e),K=!0,ee=h.currItem.bounds,Mt("x",et)||(Mt("y",et),Le(pe),Oe())}}},Ot=function(e){if(z.isOldAndroid){if(B&&"mouseup"===e.type)return;-1<e.type.indexOf("touch")&&(clearTimeout(B),B=setTimeout(function(){B=0},600))}Me("pointerUp"),gt(e,!1)&&e.preventDefault(),!E||-1<(n=f.arraySearch(it,e.pointerId,"id"))&&(a=it.splice(n,1)[0],navigator.msPointerEnabled?(a.type={4:"mouse",2:"touch",3:"pen"}[e.pointerType],a.type||(a.type=e.pointerType||"mouse")):a.type=e.pointerType||"mouse");var t=Tt(e),n=t.length;if("mouseup"===e.type&&(n=0),2===n)return!(j=null);1===n&&Fe(nt,t[0]),0!==n||ie||ne||(a||("mouseup"===e.type?a={x:e.pageX,y:e.pageY,type:"mouse"}:e.changedTouches&&e.changedTouches[0]&&(a={x:e.changedTouches[0].pageX,y:e.changedTouches[0].pageY,type:"touch"})),Me("touchRelease",e,a));var i,o,a=-1;if(0===n&&(G=!1,f.unbind(window,s,h),ft(),J?a=0:-1!==ut&&(a=Se()-ut)),ut=1===n?Se():-1,a=-1!==a&&a<150?"zoom":"swipe",J&&n<2&&(J=!1,1===n&&(a="zoomPointerUp"),Me("zoomGestureEnded")),j=null,K||V||ne||W)if($e(),(H=H||kt()).calculateSwipeSpeed("x"),W){bt()<y.verticalDragRange?h.close():(i=pe.y,o=re,je("verticalDrag",0,1,300,f.easing.cubic.out,function(e){pe.y=(h.currItem.initialPosition.y-i)*e+i,Ae((1-o)*e+o),Oe()}),Me("onVerticalDrag",1))}else{if(($||ne)&&0===n){if(Pt(a,H))return;a="zoomPointerUp"}ne||("swipe"===a?!$&&g>h.currItem.fitRatio&&Rt(H):Ft())}},kt=function(){var t,n,i={lastFlickOffset:{},lastFlickDist:{},lastFlickSpeed:{},slowDownRatio:{},slowDownRatioReverse:{},speedDecelerationRatio:{},speedDecelerationRatioAbs:{},distanceOffset:{},backAnimDestination:{},backAnimStarted:{},calculateSwipeSpeed:function(e){n=1<at.length?(t=Se()-U+50,at[at.length-2][e]):(t=Se()-N,nt[e]),i.lastFlickOffset[e]=tt[e]-n,i.lastFlickDist[e]=Math.abs(i.lastFlickOffset[e]),20<i.lastFlickDist[e]?i.lastFlickSpeed[e]=i.lastFlickOffset[e]/t:i.lastFlickSpeed[e]=0,Math.abs(i.lastFlickSpeed[e])<.1&&(i.lastFlickSpeed[e]=0),i.slowDownRatio[e]=.95,i.slowDownRatioReverse[e]=1-i.slowDownRatio[e],i.speedDecelerationRatio[e]=1},calculateOverBoundsAnimOffset:function(t,e){i.backAnimStarted[t]||(pe[t]>ee.min[t]?i.backAnimDestination[t]=ee.min[t]:pe[t]<ee.max[t]&&(i.backAnimDestination[t]=ee.max[t]),i.backAnimDestination[t]!==undefined&&(i.slowDownRatio[t]=.7,i.slowDownRatioReverse[t]=1-i.slowDownRatio[t],i.speedDecelerationRatioAbs[t]<.05&&(i.lastFlickSpeed[t]=0,i.backAnimStarted[t]=!0,je("bounceZoomPan"+t,pe[t],i.backAnimDestination[t],e||300,f.easing.sine.out,function(e){pe[t]=e,Oe()}))))},calculateAnimOffset:function(e){i.backAnimStarted[e]||(i.speedDecelerationRatio[e]=i.speedDecelerationRatio[e]*(i.slowDownRatio[e]+i.slowDownRatioReverse[e]-i.slowDownRatioReverse[e]*i.timeDiff/10),i.speedDecelerationRatioAbs[e]=Math.abs(i.lastFlickSpeed[e]*i.speedDecelerationRatio[e]),i.distanceOffset[e]=i.lastFlickSpeed[e]*i.speedDecelerationRatio[e]*i.timeDiff,pe[e]+=i.distanceOffset[e])},panAnimLoop:function(){Xe.zoomPan&&(Xe.zoomPan.raf=k(i.panAnimLoop),i.now=Se(),i.timeDiff=i.now-i.lastNow,i.lastNow=i.now,i.calculateAnimOffset("x"),i.calculateAnimOffset("y"),Oe(),i.calculateOverBoundsAnimOffset("x"),i.calculateOverBoundsAnimOffset("y"),i.speedDecelerationRatioAbs.x<.05&&i.speedDecelerationRatioAbs.y<.05&&(pe.x=Math.round(pe.x),pe.y=Math.round(pe.y),Oe(),Ke("zoomPan")))}};return i},Rt=function(e){if(e.calculateSwipeSpeed("y"),ee=h.currItem.bounds,e.backAnimDestination={},e.backAnimStarted={},Math.abs(e.lastFlickSpeed.x)<=.05&&Math.abs(e.lastFlickSpeed.y)<=.05)return e.speedDecelerationRatioAbs.x=e.speedDecelerationRatioAbs.y=0,e.calculateOverBoundsAnimOffset("x"),e.calculateOverBoundsAnimOffset("y"),!0;qe("zoomPan"),e.lastNow=Se(),e.panAnimLoop()},Pt=function(e,t){var n,i;ne||(lt=x),"swipe"===e&&(i=tt.x-nt.x,e=t.lastFlickDist.x<10,30<i&&(e||20<t.lastFlickOffset.x)?a=-1:i<-30&&(e||t.lastFlickOffset.x<-20)&&(a=1)),a&&((x+=a)<0?(x=y.loop?Ht()-1:0,o=!0):x>=Ht()&&(x=y.loop?0:Ht()-1,o=!0),o&&!y.loop||(xe+=a,fe-=a,n=!0));var o=ye.x*fe,a=Math.abs(o-ct.x),r=n||o>ct.x==0<t.lastFlickSpeed.x?(r=0<Math.abs(t.lastFlickSpeed.x)?a/Math.abs(t.lastFlickSpeed.x):333,r=Math.min(r,400),Math.max(r,250)):333;return lt===x&&(n=!1),ne=!0,Me("mainScrollAnimStart"),je("mainScroll",ct.x,o,r,f.easing.cubic.out,Pe,function(){$e(),ne=!1,lt=-1,!n&&lt===x||h.updateCurrItem(),Me("mainScrollAnimComplete")}),n&&h.updateCurrItem(!0),n},Zt=function(e){return 1/Q*e*d},Ft=function(){var e=g,t=Ue(),n=He();g<t?e=t:n<g&&(e=n);var i,o=re;return ae&&!Y&&!le&&g<t?h.close():(ae&&(i=function(e){Ae((1-o)*e+o)}),h.zoomTo(e,0,200,f.easing.cubic.out,i)),!0};Ie("Gestures",{publicMethods:{initGestures:function(){var e=function(e,t,n,i,o){D=e+t,T=e+n,M=e+i,S=o?e+o:""};(E=z.pointerEvent)&&z.touch&&(z.touch=!1),E?navigator.msPointerEnabled?e("MSPointer","Down","Move","Up","Cancel"):e("pointer","down","move","up","cancel"):z.touch?(e("touch","start","move","end","cancel"),O=!0):e("mouse","down","move","up"),s=T+" "+M+" "+S,u=D,E&&!O&&(O=1<navigator.maxTouchPoints||1<navigator.msMaxTouchPoints),h.likelyTouchDevice=O,c[D]=St,c[T]=At,c[M]=Ot,S&&(c[S]=c[M]),z.touch&&(u+=" mousedown",s+=" mousemove mouseup",c.mousedown=c[D],c.mousemove=c[T],c.mouseup=c[M]),O||(y.allowPanToNext=!1)}}});var Lt,zt,_t,Nt,Ut,Ht,Yt=function(r,e,l,t){var s;Lt&&clearTimeout(Lt),_t=Nt=!0,r.initialLayout?(s=r.initialLayout,r.initialLayout=null):s=y.getThumbBoundsFn&&y.getThumbBoundsFn(x);var u=l?y.hideAnimationDuration:y.showAnimationDuration,c=function(){Ke("initialZoom"),l?(h.template.removeAttribute("style"),h.bg.removeAttribute("style")):(Ae(1),e&&(e.style.display="block"),f.addClass(m,"pswp--animated-in"),Me("initialZoom"+(l?"OutEnd":"InEnd"))),t&&t(),Nt=!1};if(!u||!s||s.x===undefined)return Me("initialZoom"+(l?"Out":"In")),g=r.initialZoomLevel,Fe(pe,r.initialPosition),Oe(),m.style.opacity=l?0:1,Ae(1),void(u?setTimeout(function(){c()},u):c());var d,p;d=o,p=!h.currItem.src||h.currItem.loadError||y.showHideOpacity,r.miniImg&&(r.miniImg.style.webkitBackfaceVisibility="hidden"),l||(g=s.w/r.w,pe.x=s.x,pe.y=s.y-Z,h[p?"template":"bg"].style.opacity=.001,Oe()),qe("initialZoom"),l&&!d&&f.removeClass(m,"pswp--animated-in"),p&&(l?f[(d?"remove":"add")+"Class"](m,"pswp--animate_opacity"):setTimeout(function(){f.addClass(m,"pswp--animate_opacity")},30)),Lt=setTimeout(function(){var t,n,i,o,a,e;Me("initialZoom"+(l?"Out":"In")),l?(t=s.w/r.w,n=pe.x,i=pe.y,o=g,a=re,e=function(e){1===e?(g=t,pe.x=s.x,pe.y=s.y-L):(g=(t-o)*e+o,pe.x=(s.x-n)*e+n,pe.y=(s.y-L-i)*e+i),Oe(),p?m.style.opacity=1-e:Ae(a-e*a)},d?je("initialZoom",0,1,u,f.easing.cubic.out,e,c):(e(1),Lt=setTimeout(c,u+20))):(g=r.initialZoomLevel,Fe(pe,r.initialPosition),Oe(),Ae(1),p?m.style.opacity=1:Ae(1),Lt=setTimeout(c,u+20))},l?25:90)},Wt={},Bt=[],Gt={index:0,errorMsg:'<div class="pswp__error-msg"><a href="%url%" target="_blank">The image</a> could not be loaded.</div>',forceProgressiveLoading:!1,preload:[1,1],getNumItemsFn:function(){return zt.length}},Xt=function(){return{center:{x:0,y:0},max:{x:0,y:0},min:{x:0,y:0}}},Vt=function(e,t,n){if(!e.src||e.loadError)return e.w=e.h=0,e.initialZoomLevel=e.fitRatio=1,e.bounds=Xt(),e.initialPosition=e.bounds.center,e.bounds;var i,o,a,r=!n;return r&&(e.vGap||(e.vGap={top:0,bottom:0}),Me("parseVerticalMargin",e)),Wt.x=t.x,Wt.y=t.y-e.vGap.top-e.vGap.bottom,r&&(i=Wt.x/e.w,o=Wt.y/e.h,e.fitRatio=i<o?i:o,"orig"===(a=y.scaleMode)?n=1:"fit"===a&&(n=e.fitRatio),1<n&&(n=1),e.initialZoomLevel=n,e.bounds||(e.bounds=Xt())),n?(i=(t=e).w*n,o=e.h*n,(a=t.bounds).center.x=Math.round((Wt.x-i)/2),a.center.y=Math.round((Wt.y-o)/2)+t.vGap.top,a.max.x=i>Wt.x?Math.round(Wt.x-i):a.center.x,a.max.y=o>Wt.y?Math.round(Wt.y-o)+t.vGap.top:a.center.y,a.min.x=i>Wt.x?0:a.center.x,a.min.y=o>Wt.y?t.vGap.top:a.center.y,r&&n===e.initialZoomLevel&&(e.initialPosition=e.bounds.center),e.bounds):void 0},Kt=function(e,t,n,i,o,a){t.loadError||i&&(t.imageAppended=!0,jt(t,i,t===h.currItem&&be),n.appendChild(i),a&&setTimeout(function(){t&&t.loaded&&t.placeholder&&(t.placeholder.style.display="none",t.placeholder=null)},500))},qt=function(e){e.loading=!0,e.loaded=!1;var t=e.img=f.createEl("pswp__img","img"),n=function(){e.loading=!1,e.loaded=!0,e.loadComplete?e.loadComplete(e):e.img=null,t.onload=t.onerror=null,t=null};return t.onload=n,t.onerror=function(){e.loadError=!0,n()},t.src=e.src,t.alt=e.alt||"",t},$t=function(e,t){if(e.src&&e.loadError&&e.container)return t&&(e.container.innerHTML=""),e.container.innerHTML=y.errorMsg.replace("%url%",e.src),!0},jt=function(e,t,n){var i;e.src&&(t=t||e.container.lastChild,i=n?e.w:Math.round(e.w*e.fitRatio),n=n?e.h:Math.round(e.h*e.fitRatio),e.placeholder&&!e.loaded&&(e.placeholder.style.width=i+"px",e.placeholder.style.height=n+"px"),t.style.width=i+"px",t.style.height=n+"px")},Jt=function(){if(Bt.length){for(var e,t=0;t<Bt.length;t++)(e=Bt[t]).holder.index===e.index&&Kt(e.index,e.item,e.baseDiv,e.img,0,e.clearPlaceholder);Bt=[]}};Ie("Controller",{publicMethods:{lazyLoadItem:function(e){e=Ce(e);var t=Ut(e);t&&(!t.loaded&&!t.loading||b)&&(Me("gettingData",e,t),t.src&&qt(t))},initController:function(){f.extend(y,Gt,!0),h.items=zt=e,Ut=h.getItemAt,Ht=y.getNumItemsFn,y.loop,Ht()<3&&(y.loop=!1),Te("beforeChange",function(e){for(var t=y.preload,n=null===e||0<=e,i=Math.min(t[0],Ht()),o=Math.min(t[1],Ht()),a=1;a<=(n?o:i);a++)h.lazyLoadItem(x+a);for(a=1;a<=(n?i:o);a++)h.lazyLoadItem(x-a)}),Te("initialLayout",function(){h.currItem.initialLayout=y.getThumbBoundsFn&&y.getThumbBoundsFn(x)}),Te("mainScrollAnimComplete",Jt),Te("initialZoomInEnd",Jt),Te("destroy",function(){for(var e,t=0;t<zt.length;t++)(e=zt[t]).container&&(e.container=null),e.placeholder&&(e.placeholder=null),e.img&&(e.img=null),e.preloader&&(e.preloader=null),e.loadError&&(e.loaded=e.loadError=!1);Bt=null})},getItemAt:function(e){return 0<=e&&(zt[e]!==undefined&&zt[e])},allowProgressiveImg:function(){return y.forceProgressiveLoading||!O||y.mouseUsed||1200<screen.width},setContent:function(t,n){y.loop&&(n=Ce(n));var e=h.getItemAt(t.index);e&&(e.container=null);var i,o,a=h.getItemAt(n);a?(Me("gettingData",n,a),t.index=n,o=(t.item=a).container=f.createEl("pswp__zoom-wrap"),!a.src&&a.html&&(a.html.tagName?o.appendChild(a.html):o.innerHTML=a.html),$t(a),Vt(a,me),!a.src||a.loadError||a.loaded?a.src&&!a.loadError&&((i=f.createEl("pswp__img","img")).style.opacity=1,i.src=a.src,jt(a,i),Kt(0,a,o,i)):(a.loadComplete=function(e){if(r){if(t&&t.index===n){if($t(e,!0))return e.loadComplete=e.img=null,Vt(e,me),ke(e),void(t.index===x&&h.updateCurrZoomItem());e.imageAppended?!Nt&&e.placeholder&&(e.placeholder.style.display="none",e.placeholder=null):z.transform&&(ne||Nt)?Bt.push({item:e,baseDiv:o,img:e.img,index:n,holder:t,clearPlaceholder:!0}):Kt(0,e,o,e.img,0,!0)}e.loadComplete=null,e.img=null,Me("imageLoadComplete",n,e)}},f.features.transform&&(e="pswp__img pswp__img--placeholder",e+=a.msrc?"":" pswp__img--placeholder--blank",e=f.createEl(e,a.msrc?"img":""),a.msrc&&(e.src=a.msrc),jt(a,e),o.appendChild(e),a.placeholder=e),a.loading||qt(a),h.allowProgressiveImg()&&(!_t&&z.transform?Bt.push({item:a,baseDiv:o,img:a.img,index:n,holder:t}):Kt(0,a,o,a.img,0,!0))),_t||n!==x?ke(a):(te=o.style,Yt(a,i||a.img)),t.el.innerHTML="",t.el.appendChild(o)):t.el.innerHTML=""},cleanSlide:function(e){e.img&&(e.img.onload=e.img.onerror=null),e.loaded=e.loading=e.img=e.imageAppended=!1}}});var Qt,en,tn={},nn=function(e,t,n){var i=document.createEvent("CustomEvent"),n={origEvent:e,target:e.target,releasePoint:t,pointerType:n||"touch"};i.initCustomEvent("pswpTap",!0,!0,n),e.target.dispatchEvent(i)};Ie("Tap",{publicMethods:{initTap:function(){Te("firstTouchStart",h.onTapStart),Te("touchRelease",h.onTapRelease),Te("destroy",function(){tn={},Qt=null})},onTapStart:function(e){1<e.length&&(clearTimeout(Qt),Qt=null)},onTapRelease:function(e,t){var n,i,o;t&&(K||X||Ve||(n=t,Qt&&(clearTimeout(Qt),Qt=null,i=n,o=tn,Math.abs(i.x-o.x)<25&&Math.abs(i.y-o.y)<25)?Me("doubleTap",n):"mouse"!==t.type?"BUTTON"===e.target.tagName.toUpperCase()||f.hasClass(e.target,"pswp__single-tap")?nn(e,t):(Fe(tn,n),Qt=setTimeout(function(){nn(e,t),Qt=null},300)):nn(e,t,"mouse")))}}}),Ie("DesktopZoom",{publicMethods:{initDesktopZoom:function(){F||(O?Te("mouseUsed",function(){h.setupDesktopZoom()}):h.setupDesktopZoom(!0))},setupDesktopZoom:function(e){en={};var t="wheel mousewheel DOMMouseScroll";Te("bindEvents",function(){f.bind(m,t,h.handleMouseWheel)}),Te("unbindEvents",function(){en&&f.unbind(m,t,h.handleMouseWheel)}),h.mouseZoomedIn=!1;var n,i=function(){h.mouseZoomedIn&&(f.removeClass(m,"pswp--zoomed-in"),h.mouseZoomedIn=!1),g<1?f.addClass(m,"pswp--zoom-allowed"):f.removeClass(m,"pswp--zoom-allowed"),o()},o=function(){n&&(f.removeClass(m,"pswp--dragging"),n=!1)};Te("resize",i),Te("afterChange",i),Te("pointerDown",function(){h.mouseZoomedIn&&(n=!0,f.addClass(m,"pswp--dragging"))}),Te("pointerUp",o),e||i()},handleMouseWheel:function(e){if(g<=h.currItem.fitRatio)return y.modal&&(!y.closeOnScroll||Ve||G?e.preventDefault():A&&2<Math.abs(e.deltaY)&&(o=!0,h.close())),!0;if(e.stopPropagation(),en.x=0,"deltaX"in e)1===e.deltaMode?(en.x=18*e.deltaX,en.y=18*e.deltaY):(en.x=e.deltaX,en.y=e.deltaY);else if("wheelDelta"in e)e.wheelDeltaX&&(en.x=-.16*e.wheelDeltaX),e.wheelDeltaY?en.y=-.16*e.wheelDeltaY:en.y=-.16*e.wheelDelta;else{if(!("detail"in e))return;en.y=e.detail}Ne(g,!0);var t=pe.x-en.x,n=pe.y-en.y;(y.modal||t<=ee.min.x&&t>=ee.max.x&&n<=ee.min.y&&n>=ee.max.y)&&e.preventDefault(),h.panTo(t,n)},toggleDesktopZoom:function(e){e=e||{x:me.x/2+he.x,y:me.y/2+he.y};var t=y.getDoubleTapZoom(!0,h.currItem),n=g===t;h.mouseZoomedIn=!n,h.zoomTo(n?h.currItem.initialZoomLevel:t,e,333),f[(n?"remove":"add")+"Class"](m,"pswp--zoomed-in")}}});var on,an,rn,ln,sn,un,cn,dn,pn,mn,fn,hn,yn={history:!0,galleryUID:1},xn=function(){return fn.hash.substring(1)},gn=function(){on&&clearTimeout(on),rn&&clearTimeout(rn)},vn=function(){var e=xn(),t={};if(e.length<5)return t;var n,i=e.split("&");for(a=0;a<i.length;a++){i[a]&&((n=i[a].split("=")).length<2||(t[n[0]]=n[1]))}if(y.galleryPIDs){for(var o=t.pid,a=t.pid=0;a<zt.length;a++)if(zt[a].pid===o){t.pid=a;break}}else t.pid=parseInt(t.pid,10)-1;return t.pid<0&&(t.pid=0),t},wn=function(){var e,t;rn&&clearTimeout(rn),Ve||G?rn=setTimeout(wn,500):(ln?clearTimeout(an):ln=!0,t=x+1,(e=Ut(x)).hasOwnProperty("pid")&&(t=e.pid),e=cn+"&gid="+y.galleryUID+"&pid="+t,dn||-1===fn.hash.indexOf(e)&&(mn=!0),t=fn.href.split("#")[0]+"#"+e,hn?"#"+e!==window.location.hash&&history[dn?"replaceState":"pushState"]("",document.title,t):dn?fn.replace(t):fn.hash=e,dn=!0,an=setTimeout(function(){ln=!1},60))};Ie("History",{publicMethods:{initHistory:function(){var e,t;f.extend(y,yn,!0),y.history&&(fn=window.location,dn=pn=mn=!1,cn=xn(),hn="pushState"in history,-1<cn.indexOf("gid=")&&(cn=(cn=cn.split("&gid=")[0]).split("?gid=")[0]),Te("afterChange",h.updateURL),Te("unbindEvents",function(){f.unbind(window,"hashchange",h.onHashChange)}),e=function(){un=!0,pn||(mn?history.back():cn?fn.hash=cn:hn?history.pushState("",document.title,fn.pathname+fn.search):fn.hash=""),gn()},Te("unbindEvents",function(){o&&e()}),Te("destroy",function(){un||e()}),Te("firstUpdate",function(){x=vn().pid}),-1<(t=cn.indexOf("pid="))&&"&"===(cn=cn.substring(0,t)).slice(-1)&&(cn=cn.slice(0,-1)),setTimeout(function(){r&&f.bind(window,"hashchange",h.onHashChange)},40))},onHashChange:function(){if(xn()===cn)return pn=!0,void h.close();ln||(sn=!0,h.goTo(vn().pid),sn=!1)},updateURL:function(){gn(),sn||(dn?on=setTimeout(wn,800):wn())}}}),f.extend(h,t)}});