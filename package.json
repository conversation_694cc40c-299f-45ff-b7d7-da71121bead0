{"name": "filament-demo", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@alpinejs/focus": "^3.10.3", "@faker-js/faker": "^9.3.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.9", "alpinejs": "^3.10.3", "autoprefixer": "^10.4.14", "laravel-vite-plugin": "^0.5.0", "postcss": "^8.4.28", "postcss-nesting": "^12.0.1", "tailwindcss": "^3.3.3", "tippy.js": "^6.3.7", "vite": "^3.0.0"}, "dependencies": {"exceljs": "^4.4.0", "faker": "^6.6.6", "puppeteer": "^23.9.0"}}