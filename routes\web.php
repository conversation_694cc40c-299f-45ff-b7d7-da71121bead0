<?php

use App\Livewire\Form;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ShopController;
use App\Http\Controllers\CartController;
use App\Mail\MyTestEmail;
use App\Http\Controllers\ContactController;
use Illuminate\Support\Facades\Mail;
use \App\Http\Controllers\BlogController;
use App\Http\Controllers\NewsletterController;

Route::get('form', Form::class);

//Route::view('/', 'pages.home')->name('index');
Route::get('/', [ShopController::class, 'index'])->name('index');


Route::get('/products', [ProductController::class, 'index'])->name('products.index');
Route::get('/products/{id}', [ProductController::class, 'show'])->name('products.show');

//Route::get('/contact', function () {
//    return view('pages.contact-us');
//})->name('contact');

Route::get('/contact', [ContactController::class, 'create'])->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');





Route::get('/shop', [ShopController::class, 'showDiksonProducts'])->name('shop.index');

Route::get('/shop/muster', [ShopController::class, 'showMusterProducts'])->name('shop.muster');
Route::get('/shop/dikson', [ShopController::class, 'showDiksonProducts'])->name('shop.dikson');
Route::get('/shop/muster/benexere', [ShopController::class, 'showMusterBenexereProducts'])->name('shop.muster.benexere');



// Cart routes
Route::get('/cart', [CartController::class, 'showCart'])->name('cart.show');
Route::post('/cart/add', [CartController::class, 'addToCart'])->name('cart.add');
Route::get('/cart/data', [CartController::class, 'getCartData'])->name('cart.data');
Route::delete('/cart/item/{id}', [CartController::class, 'removeFromCart'])->name('cart.item.remove');
//Route::post('/cart/updatess', [CartController::class, 'updateCartItem'])->name('cart.updatesss');
Route::delete('/cart/remove/{id}', [CartController::class, 'removeCartItem'])->name('cart.remove');
//Route::post('/cart/update', [CartController::class, 'updateQuantity'])->name('cart.update');
Route::post('/cart/update', [CartController::class, 'update'])->name('cart.update');



Route::get('/checkout', [CartController::class, 'checkout'])->name('checkout');
Route::get('/thank-you', [ShopController::class, 'tahnkYou'])->name('thankyou');

Route::get('/about-us', function () {
    return view('pages.about_us');
})->name('about_us');

Route::post('/place-order', [ShopController::class, 'store'])->name('order.store');

//Pages
//Route::get('/hairstyle', function () {
//    return view('pages.hairstyle');
//})->name('hairstyle');

Route::get('/hairstyle', [ShopController::class, 'hairstyle'])->name('hairstyle');


Route::get('/nos-marques', [ShopController::class, 'ourbrands'])->name('ourbrands');

// Brand specific pages
Route::get('/brands/dikson', function () {
    return view('pages.brands.dikson');
})->name('brand.dikson');

Route::get('/brands/electric', function () {
    return view('pages.brands.electric');
})->name('brand.electric');

Route::get('/brands/benexere', function () {
    return view('pages.brands.benexere');
})->name('brand.benexere');


Route::get('/beauty', [ShopController::class, 'beauty'])->name('beauty');


Route::get('/hygiene-safety', function () {
    return view('pages.coming-soon');
})->name('hygiene-safety');

Route::get('/video', function () {
    return view('pages.coming-soon');
})->name('video');

Route::get('/downloads', function () {
    return view('pages.coming-soon');
})->name('downloads');


Route::get('categories', [ShopController::class, 'showAllCategorie'])->name('categories.index');
Route::get('categories/{category}', [ShopController::class, 'showCategorie'])->name('categories.show');

Route::get('/blog', [BlogController::class, 'index'])->name('blog');
Route::get('/post', [BlogController::class, 'recentelyPost'])->name('recentelyPost.show');
Route::get('/posts/{id}', [BlogController::class, 'show'])->name('posts.show');


Route::get('/testroute', function() {
    $name = "Funny Coder";

    Mail::to('<EMAIL>')->send(new MyTestEmail($name));
});

// Newsletter routes
Route::post('/newsletter/subscribe', [NewsletterController::class, 'subscribe'])->name('newsletter.subscribe');
Route::get('/newsletter/unsubscribe/{email}', [NewsletterController::class, 'unsubscribe'])->name('newsletter.unsubscribe');



