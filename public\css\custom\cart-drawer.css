/* <PERSON>t Drawer Styles */
.cart-drawer {
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease;
}

.cart-drawer.active {
    visibility: visible;
    opacity: 1;
}

.cart-drawer-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    cursor: pointer;
}

.cart-drawer-content {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    max-width: 420px;
    height: 100%;
    background-color: #fff;
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
}

.cart-drawer.active .cart-drawer-content {
    transform: translateX(0);
}

/* Cart Drawer Header */
.cart-drawer-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f8f9fa;
}

.cart-drawer-title {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
    color: #1A2A3A;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cart-drawer-title i {
    color: #20c7d9;
}

.cart-drawer-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.cart-drawer-close:hover {
    background-color: #e9ecef;
    color: #1A2A3A;
}

/* Cart Drawer Body */
.cart-drawer-body {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

/* Empty State */
.cart-empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-cart-icon {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.cart-empty-state h4 {
    color: #6c757d;
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.cart-empty-state p {
    color: #adb5bd;
    margin-bottom: 2rem;
    font-size: 1rem;
}

/* Cart Items List */
.cart-items-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.cart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background-color: #fff;
    transition: all 0.3s ease;
    transform: translateX(0);
    opacity: 1;
}

.cart-item:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.cart-item.removing {
    transform: translateX(-100%);
    opacity: 0;
    margin-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
    border-width: 0;
}

.cart-item-image {
    width: 60px;
    height: 60px;
    border-radius: 6px;
    overflow: hidden;
    flex-shrink: 0;
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-details {
    flex: 1;
    min-width: 0;
}

.cart-item-name {
    font-weight: 600;
    color: #1A2A3A;
    margin-bottom: 0.25rem;
    font-size: 1.3rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cart-item-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.5rem;
}

.cart-item-quantity {
    font-size: 1.2rem;
    color: #6c757d;
    font-weight: 500;
}

.cart-item-price {
    font-weight: 600;
    color: #20c7d9;
    font-size: 1.4rem;
}

.cart-item-actions {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.cart-item-remove {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
}

.cart-item-remove:hover {
    background-color: #dc3545;
    color: #fff;
    transform: scale(1.1);
}

.cart-item-remove:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.cart-item-remove:disabled:hover {
    background-color: transparent;
    color: #dc3545;
}

/* Cart Drawer Footer */
.cart-drawer-footer {
    padding: 1.5rem;
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

.cart-total-section {
    margin-bottom: 1.5rem;
}

.cart-subtotal {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.2rem;
    font-weight: 600;
    color: #1A2A3A;
}

.cart-total-amount {
    color: #20c7d9;
}

.cart-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.cart-actions .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    font-weight: 600;
    font-size: 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.cart-actions .btn-outline-primary {
    border: 2px solid #20c7d9;
    color: #20c7d9;
    background-color: transparent;
}

.cart-actions .btn-outline-primary:hover {
    background-color: #20c7d9;
    color: #fff;
}

.cart-actions .btn-primary {
    background-color: #20c7d9;
    border: 2px solid #20c7d9;
    color: #fff;
}

.cart-actions .btn-primary:hover {
    background-color: #1ba8b8;
    border-color: #1ba8b8;
}

/* Toast Notification */
.toast-notification {
    position: fixed;
    top: 100px;
    right: 20px;
    z-index: 10000;
    background-color: #28a745;
    color: #fff;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
}

.toast-notification.success {
    background-color: #28a745;
}

.toast-notification.error {
    background-color: #dc3545;
}

.toast-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.toast-content i {
    font-size: 1.25rem;
}

.toast-message {
    font-weight: 600;
    font-size: 1rem;
}

/* Cart Icon Animation */
.cart-icon {
    transition: transform 0.3s ease;
}

.cart-icon.bounce {
    animation: cartBounce 0.6s ease;
}

@keyframes cartBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Cart Count Badge Animation */
.cart-count {
    transition: all 0.3s ease;
}

.cart-count.pulse {
    animation: countPulse 0.6s ease;
}

@keyframes countPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.3);
        background-color: #28a745;
    }
    100% {
        transform: scale(1);
    }
}

/* Loading State */
.cart-drawer-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.cart-drawer-loading i {
    font-size: 2rem;
    margin-right: 0.5rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Smooth transitions for cart updates */
.cart-count, .cart-price {
    transition: all 0.3s ease;
}

.cart-count.updating, .cart-price.updating {
    transform: scale(1.1);
    color: #28a745;
}

/* Enhanced button styles */
.btn-block {
    width: 100%;
    text-decoration: none;
}

/* Improved cart item hover effects */
.cart-item {
    position: relative;
    overflow: hidden;
}

.cart-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(32, 199, 217, 0.1), transparent);
    transition: left 0.5s ease;
}

.cart-item:hover::before {
    left: 100%;
}

/* Toast notification improvements */
.toast-notification {
    min-width: 300px;
    max-width: 400px;
}

@media (max-width: 480px) {
    .toast-notification {
        right: 10px;
        left: 10px;
        min-width: auto;
        max-width: none;
        transform: translateY(-100px);
        opacity: 0;
    }

    .toast-notification.show {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Cart Button Icon Styles - Matching Original Design */
.btn-cart-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3.6rem;
    height: 3.6rem;
    margin-bottom: 0.5rem;
    border: 1px solid #e1e1e1;
    border-radius: 50%;
    background-color: #fff;
    color: #999;
    font-size: 1.6rem;
    font-weight: 700;
    transition: border-color 0.3s, color 0.3s, background-color 0.3s;
    cursor: pointer;
}

.btn-cart-icon:hover {
    border-color: #20c7d9;
    color: #fff;
    background-color: #20c7d9;
}

.btn-cart-icon:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-cart-icon:disabled:hover {
    border-color: #e1e1e1;
    color: #999;
    background-color: #fff;
}

.btn-cart-icon:not(.btn-wishlist) i {
    margin-bottom: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cart-drawer-content {
        max-width: 100%;
        width: 100%;
    }

    .cart-drawer-header {
        padding: 1rem;
    }

    .cart-drawer-body {
        padding: 0.75rem;
    }

    .cart-drawer-footer {
        padding: 1rem;
    }

    .cart-item {
        padding: 0.75rem;
    }

    .cart-item-image {
        width: 50px;
        height: 50px;
    }

    .cart-drawer-title {
        font-size: 1.25rem;
    }

    .cart-item-name {
        font-size: 1.05rem;
    }

    .cart-item-quantity {
        font-size: 1rem;
    }

    .cart-item-price {
        font-size: 0.95rem;
    }

    .cart-subtotal {
        font-size: 1.1rem;
    }

    /* Cart button responsive styles */
    .btn-cart-icon {
        width: 3.2rem;
        height: 3.2rem;
        font-size: 1.3rem;
    }
}

/* Very small screens */
@media (max-width: 480px) {
    .cart-drawer-header {
        padding: 0.75rem;
    }

    .cart-drawer-body {
        padding: 0.5rem;
    }

    .cart-drawer-footer {
        padding: 0.75rem;
    }

    .cart-item {
        padding: 0.5rem;
        gap: 0.75rem;
    }

    .cart-item-image {
        width: 45px;
        height: 45px;
    }

    .cart-item-name {
        font-size: 0.85rem;
    }

    .cart-item-meta {
        font-size: 0.8rem;
    }
}
