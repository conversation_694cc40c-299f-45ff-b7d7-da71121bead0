/*! PhotoSwipe Default UI - 4.1.3 - 2019-01-08
* http://photoswipe.com
* Copyright (c) 2019 <PERSON>; */
!function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.PhotoSwipeUI_Default=t()}(this,function(){"use strict";return function(o,s){var n,a,l,r,t,i,u,c,p,e,d,m,f,h,w,g,b,v,_=this,C=!1,T=!0,I=!0,E={barsSize:{top:44,bottom:"auto"},closeElClasses:["item","caption","zoom-wrap","ui","top-bar"],timeToIdle:4e3,timeToIdleOutside:1e3,loadingIndicatorDelay:1e3,addCaptionHTMLFn:function(e,t){return e.title?(t.children[0].innerHTML=e.title,!0):(t.children[0].innerHTML="",!1)},closeEl:!0,captionEl:!0,fullscreenEl:!0,zoomEl:!0,shareEl:!0,counterEl:!0,arrowEl:!0,preloaderEl:!0,tapToClose:!1,tapToToggleControls:!0,clickToCloseNonZoomable:!0,shareButtons:[{id:"facebook",label:"Share on Facebook",url:"https://www.facebook.com/sharer/sharer.php?u={{url}}"},{id:"twitter",label:"Tweet",url:"https://twitter.com/intent/tweet?text={{text}}&url={{url}}"},{id:"pinterest",label:"Pin it",url:"http://www.pinterest.com/pin/create/button/?url={{url}}&media={{image_url}}&description={{text}}"},{id:"download",label:"Download image",url:"{{raw_image_url}}",download:!0}],getImageURLForShare:function(){return o.currItem.src||""},getPageURLForShare:function(){return window.location.href},getTextForShare:function(){return o.currItem.title||""},indexIndicatorSep:" / ",fitControlsWidth:1200},F=function(e){if(g)return!0;e=e||window.event,w.timeToIdle&&w.mouseUsed&&!p&&z();for(var t,n,o=(e.target||e.srcElement).getAttribute("class")||"",l=0;l<P.length;l++)(t=P[l]).onTap&&-1<o.indexOf("pswp__"+t.name)&&(t.onTap(),n=!0);n&&(e.stopPropagation&&e.stopPropagation(),g=!0,e=s.features.isOldAndroid?600:30,setTimeout(function(){g=!1},e))},x=function(e,t,n){s[(n?"add":"remove")+"Class"](e,"pswp__"+t)},S=function(){var e=1===w.getNumItemsFn();e!==h&&(x(a,"ui--one-slide",e),h=e)},k=function(){x(u,"share-modal--hidden",I)},K=function(){return(I=!I)?(s.removeClass(u,"pswp__share-modal--fade-in"),setTimeout(function(){I&&k()},300)):(k(),setTimeout(function(){I||s.addClass(u,"pswp__share-modal--fade-in")},30)),I||O(),!1},L=function(e){var t=(e=e||window.event).target||e.srcElement;return o.shout("shareLinkClick",e,t),!!t.href&&(!!t.hasAttribute("download")||(window.open(t.href,"pswp_share","scrollbars=yes,resizable=yes,toolbar=no,location=yes,width=550,height=420,top=100,left="+(window.screen?Math.round(screen.width/2-275):100)),I||K(),!1))},O=function(){for(var e,t,n,o,l="",r=0;r<w.shareButtons.length;r++)e=w.shareButtons[r],t=w.getImageURLForShare(e),n=w.getPageURLForShare(e),o=w.getTextForShare(e),l+='<a href="'+e.url.replace("{{url}}",encodeURIComponent(n)).replace("{{image_url}}",encodeURIComponent(t)).replace("{{raw_image_url}}",t).replace("{{text}}",encodeURIComponent(o))+'" target="_blank" class="pswp__share--'+e.id+'"'+(e.download?"download":"")+">"+e.label+"</a>",w.parseShareButtonOut&&(l=w.parseShareButtonOut(e,l));u.children[0].innerHTML=l,u.children[0].onclick=L},R=function(e){for(var t=0;t<w.closeElClasses.length;t++)if(s.hasClass(e,"pswp__"+w.closeElClasses[t]))return!0},y=0,z=function(){clearTimeout(v),y=0,p&&_.setIdle(!1)},M=function(e){e=(e=e||window.event).relatedTarget||e.toElement;e&&"HTML"!==e.nodeName||(clearTimeout(v),v=setTimeout(function(){_.setIdle(!0)},w.timeToIdleOutside))},D=function(e){m!==e&&(x(d,"preloader--active",!e),m=e)},A=function(e){var t,n=e.vGap;!o.likelyTouchDevice||w.mouseUsed||screen.width>w.fitControlsWidth?(t=w.barsSize,w.captionEl&&"auto"===t.bottom?(r||((r=s.createEl("pswp__caption pswp__caption--fake")).appendChild(s.createEl("pswp__caption__center")),a.insertBefore(r,l),s.addClass(a,"pswp__ui--fit")),w.addCaptionHTMLFn(e,r,!0)?(e=r.clientHeight,n.bottom=parseInt(e,10)||44):n.bottom=t.top):n.bottom="auto"===t.bottom?0:t.bottom,n.top=t.top):n.top=n.bottom=0},P=[{name:"caption",option:"captionEl",onInit:function(e){l=e}},{name:"share-modal",option:"shareEl",onInit:function(e){u=e},onTap:function(){K()}},{name:"button--share",option:"shareEl",onInit:function(e){i=e},onTap:function(){K()}},{name:"button--zoom",option:"zoomEl",onTap:o.toggleDesktopZoom},{name:"counter",option:"counterEl",onInit:function(e){t=e}},{name:"button--close",option:"closeEl",onTap:o.close},{name:"button--arrow--left",option:"arrowEl",onTap:o.prev},{name:"button--arrow--right",option:"arrowEl",onTap:o.next},{name:"button--fs",option:"fullscreenEl",onTap:function(){n.isFullscreen()?n.exit():n.enter()}},{name:"preloader",option:"preloaderEl",onInit:function(e){d=e}}];_.init=function(){var t;s.extend(o.options,E,!0),w=o.options,a=s.getChildByClass(o.scrollWrap,"pswp__ui"),(e=o.listen)("onVerticalDrag",function(e){T&&e<.95?_.hideControls():!T&&.95<=e&&_.showControls()}),e("onPinchClose",function(e){T&&e<.9?(_.hideControls(),t=!0):t&&!T&&.9<e&&_.showControls()}),e("zoomGestureEnded",function(){(t=!1)&&!T&&_.showControls()}),e("beforeChange",_.update),e("doubleTap",function(e){var t=o.currItem.initialZoomLevel;o.getZoomLevel()!==t?o.zoomTo(t,e,333):o.zoomTo(w.getDoubleTapZoom(!1,o.currItem),e,333)}),e("preventDragEvent",function(e,t,n){var o=e.target||e.srcElement;o&&o.getAttribute("class")&&-1<e.type.indexOf("mouse")&&(0<o.getAttribute("class").indexOf("__caption")||/(SMALL|STRONG|EM)/i.test(o.tagName))&&(n.prevent=!1)}),e("bindEvents",function(){s.bind(a,"pswpTap click",F),s.bind(o.scrollWrap,"pswpTap",_.onGlobalTap),o.likelyTouchDevice||s.bind(o.scrollWrap,"mouseover",_.onMouseOver)}),e("unbindEvents",function(){I||K(),b&&clearInterval(b),s.unbind(document,"mouseout",M),s.unbind(document,"mousemove",z),s.unbind(a,"pswpTap click",F),s.unbind(o.scrollWrap,"pswpTap",_.onGlobalTap),s.unbind(o.scrollWrap,"mouseover",_.onMouseOver),n&&(s.unbind(document,n.eventK,_.updateFullscreen),n.isFullscreen()&&(w.hideAnimationDuration=0,n.exit()),n=null)}),e("destroy",function(){w.captionEl&&(r&&a.removeChild(r),s.removeClass(l,"pswp__caption--empty")),u&&(u.children[0].onclick=null),s.removeClass(a,"pswp__ui--over-close"),s.addClass(a,"pswp__ui--hidden"),_.setIdle(!1)}),w.showAnimationDuration||s.removeClass(a,"pswp__ui--hidden"),e("initialZoomIn",function(){w.showAnimationDuration&&s.removeClass(a,"pswp__ui--hidden")}),e("initialZoomOut",function(){s.addClass(a,"pswp__ui--hidden")}),e("parseVerticalMargin",A),function(){var l,r,i,e=function(e){if(e)for(var t=e.length,n=0;n<t;n++){l=e[n],r=l.className;for(var o=0;o<P.length;o++)i=P[o],-1<r.indexOf("pswp__"+i.name)&&(w[i.option]?(s.removeClass(l,"pswp__element--disabled"),i.onInit&&i.onInit(l)):s.addClass(l,"pswp__element--disabled"))}};e(a.children);var t=s.getChildByClass(a,"pswp__top-bar");t&&e(t.children)}(),w.shareEl&&i&&u&&(I=!0),S(),w.timeToIdle&&e("mouseUsed",function(){s.bind(document,"mousemove",z),s.bind(document,"mouseout",M),b=setInterval(function(){2===++y&&_.setIdle(!0)},w.timeToIdle/2)}),w.fullscreenEl&&!s.features.isOldAndroid&&((n=n||_.getFullscreenAPI())?(s.bind(document,n.eventK,_.updateFullscreen),_.updateFullscreen(),s.addClass(o.template,"pswp--supports-fs")):s.removeClass(o.template,"pswp--supports-fs")),w.preloaderEl&&(D(!0),e("beforeChange",function(){clearTimeout(f),f=setTimeout(function(){o.currItem&&o.currItem.loading?o.allowProgressiveImg()&&(!o.currItem.img||o.currItem.img.naturalWidth)||D(!1):D(!0)},w.loadingIndicatorDelay)}),e("imageLoadComplete",function(e,t){o.currItem===t&&D(!0)}))},_.setIdle=function(e){x(a,"ui--idle",p=e)},_.update=function(){C=!(!T||!o.currItem)&&(_.updateIndexIndicator(),w.captionEl&&(w.addCaptionHTMLFn(o.currItem,l),x(l,"caption--empty",!o.currItem.title)),!0),I||K(),S()},_.updateFullscreen=function(e){e&&setTimeout(function(){o.setScrollOffset(0,s.getScrollY())},50),s[(n.isFullscreen()?"add":"remove")+"Class"](o.template,"pswp--fs")},_.updateIndexIndicator=function(){w.counterEl&&(t.innerHTML=o.getCurrentIndex()+1+w.indexIndicatorSep+w.getNumItemsFn())},_.onGlobalTap=function(e){var t=(e=e||window.event).target||e.srcElement;g||(e.detail&&"mouse"===e.detail.pointerType?R(t)?o.close():s.hasClass(t,"pswp__img")&&(1===o.getZoomLevel()&&o.getZoomLevel()<=o.currItem.fitRatio?w.clickToCloseNonZoomable&&o.close():o.toggleDesktopZoom(e.detail.releasePoint)):(w.tapToToggleControls&&(T?_.hideControls():_.showControls()),w.tapToClose&&(s.hasClass(t,"pswp__img")||R(t))&&o.close()))},_.onMouseOver=function(e){e=(e=e||window.event).target||e.srcElement;x(a,"ui--over-close",R(e))},_.hideControls=function(){s.addClass(a,"pswp__ui--hidden"),T=!1},_.showControls=function(){T=!0,C||_.update(),s.removeClass(a,"pswp__ui--hidden")},_.supportsFullscreen=function(){var e=document;return!!(e.exitFullscreen||e.mozCancelFullScreen||e.webkitExitFullscreen||e.msExitFullscreen)},_.getFullscreenAPI=function(){var e,t=document.documentElement,n="fullscreenchange";return t.requestFullscreen?e={enterK:"requestFullscreen",exitK:"exitFullscreen",elementK:"fullscreenElement",eventK:n}:t.mozRequestFullScreen?e={enterK:"mozRequestFullScreen",exitK:"mozCancelFullScreen",elementK:"mozFullScreenElement",eventK:"moz"+n}:t.webkitRequestFullscreen?e={enterK:"webkitRequestFullscreen",exitK:"webkitExitFullscreen",elementK:"webkitFullscreenElement",eventK:"webkit"+n}:t.msRequestFullscreen&&(e={enterK:"msRequestFullscreen",exitK:"msExitFullscreen",elementK:"msFullscreenElement",eventK:"MSFullscreenChange"}),e&&(e.enter=function(){if(c=w.closeOnScroll,w.closeOnScroll=!1,"webkitRequestFullscreen"!==this.enterK)return o.template[this.enterK]();o.template[this.enterK](Element.ALLOW_KEYBOARD_INPUT)},e.exit=function(){return w.closeOnScroll=c,document[this.exitK]()},e.isFullscreen=function(){return document[this.elementK]}),e}}});