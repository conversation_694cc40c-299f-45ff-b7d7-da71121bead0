!function(e){var t={};$body=e(document.body),t.init=function(){$body.find(".code-content").each((function(){$this=e(this);var t=this.outerHTML,o=t.lastIndexOf("<"),n=t.slice(0,t.length-1).lastIndexOf(">"),i=new RegExp(String(" ").repeat(o-n-2),"gi"),c=t.replace(i,"");c=(c=c.replace(/&quot;/gi,"'")).replace(/ code-content/,"");var d=$this.closest(".code-template").find(".code-style").text().trim();if(""!=d){o=d.lastIndexOf("}");var r=d.slice(0,o).lastIndexOf("}"),a=d.slice(0,o).lastIndexOf(";");i=r<a?new RegExp(String(" ").repeat(o-a-2),"gi"):new RegExp(String(" ").repeat(o-r-2),"gi"),c="<style>\n"+d.replace(i,"")+"\n</style>\n\n"+c}$this.attr("data-html-code",c)})),$body.on("click",".show-code",t.event)},t.event=function(o){o.preventDefault();var n=e(this).closest(".code-template").find(".code-content");e(".code-popup #textareaCode").text(n.attr("data-html-code")),Riode.popup({items:{src:"#code-popup"},type:"inline",tLoading:"",mainClass:"code-console"}),t.filterMirror(),e(".copy-icon").on("click",(function(t){t.preventDefault(),e(".code-text").trigger("select"),document.execCommand("copy"),e(".code-popup .tooltiptext").text("Copied")}))},t.filterMirror=function(){e(".CodeMirror").remove(),CodeMirror.fromTextArea(document.getElementById("textareaCode"),{mode:"text/html",htmlMode:!0,lineWrapping:!1,smartIndent:!1,spellcheck:!0,addModeClass:!0,readOnly:!0}),e(".code-popup .tooltiptext").text("Copy to Clipboard")},e(window).on("load",(function(){t.init()}))}(jQuery);