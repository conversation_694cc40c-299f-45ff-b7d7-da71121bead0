/* Stock Alert Styles - Friendly & Soft Design */

/* Stock alerts for product detail pages */
.stock-alert {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
    margin-top: 1.5rem;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    animation: fadeInAlert 0.4s ease-in-out;
}

.stock-alert i {
    font-size: 1.1rem;
}

.stock-alert.out-of-stock {
    background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 100%);
    color: #6b7280;
    border-left: 4px solid #e5e7eb;
}

.stock-alert.out-of-stock i {
    color: #9ca3af;
}

.stock-alert.low-stock {
    background: linear-gradient(135deg, #fefdf8 0%, #fef9e7 100%);
    color: #92400e;
    border-left: 4px solid #fbbf24;
}

.stock-alert.low-stock i {
    color: #f59e0b;
}

/* Stock alerts for product cards */
.stock-alert-card {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.3rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 0.75rem;
    border: none;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    animation: fadeInAlert 0.4s ease-in-out;
}

.stock-alert-card i {
    font-size: 0.8rem;
}

.stock-alert-card.out-of-stock {
    background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 100%);
    color: #6b7280;
}

.stock-alert-card.out-of-stock i {
    color: #9ca3af;
}

.stock-alert-card.low-stock {
    background: linear-gradient(135deg, #fefdf8 0%, #fef9e7 100%);
    color: #92400e;
}

.stock-alert-card.low-stock i {
    color: #f59e0b;
}

/* Animation for stock alerts - Gentle and smooth */
@keyframes fadeInAlert {
    from {
        opacity: 0;
        transform: translateY(-5px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stock-alert {
        font-size: 0.9rem;
        padding: 0.6rem 0.8rem;
    }

    .stock-alert-card {
        font-size: 0.75rem;
        padding: 0.3rem 0.6rem;
    }
}

/* Dark mode support - Softer colors */
@media (prefers-color-scheme: dark) {
    .stock-alert.out-of-stock {
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        color: #d1d5db;
        border-left-color: #6b7280;
    }

    .stock-alert.low-stock {
        background: linear-gradient(135deg, #292524 0%, #44403c 100%);
        color: #fbbf24;
        border-left-color: #f59e0b;
    }

    .stock-alert-card.out-of-stock {
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        color: #d1d5db;
    }

    .stock-alert-card.low-stock {
        background: linear-gradient(135deg, #292524 0%, #44403c 100%);
        color: #fbbf24;
    }
}

/* Gentle hover effects */
.product:hover .stock-alert-card {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease-out;
}

.stock-alert-card {
    transition: all 0.2s ease-out;
}

/* Additional styling for better integration */
.stock-alert-card + .product-price {
    margin-top: 0.5rem;
}

.stock-alert + .product-rating {
    margin-top: 0;
}

/* Soft pulsing effect for low stock (very subtle) */
.stock-alert-card.low-stock {
    animation: fadeInAlert 0.4s ease-in-out, softPulse 3s ease-in-out infinite;
}

@keyframes softPulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.9;
    }
}
